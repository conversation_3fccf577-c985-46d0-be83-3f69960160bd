# 🔥 Configuración de Firebase para TiendaWeb

## 🚨 **Estado Actual**

**Firebase está DESHABILITADO** por defecto para evitar errores durante el desarrollo. La aplicación funciona con datos de muestra (mock data).

## ⚙️ **Cómo Habilitar Firebase**

### **Paso 1: Crear Proyecto en Firebase**

1. Ve a [Firebase Console](https://console.firebase.google.com/)
2. Haz clic en "Crear un proyecto"
3. Sigue el asistente de configuración
4. Habilita los siguientes servicios:
   - **Firestore Database**
   - **Authentication**
   - **Storage** (opcional)

### **Paso 2: Obtener Credenciales**

1. En tu proyecto de Firebase, ve a **Configuración del proyecto** (⚙️)
2. En la pestaña **General**, busca "Tus aplicaciones"
3. Haz clic en "Agregar aplicación" → "Web" (</>) 
4. Registra tu aplicación con un nombre
5. Copia las credenciales que aparecen

### **Paso 3: Configurar en TiendaWeb**

#### **Opción A: Variables de Entorno (Recomendado)**

Crea un archivo `.env.local` en la raíz del proyecto:

```env
# Firebase Configuration
VITE_FIREBASE_API_KEY=tu-api-key-aqui
VITE_FIREBASE_AUTH_DOMAIN=tu-proyecto.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=tu-proyecto-id
VITE_FIREBASE_STORAGE_BUCKET=tu-proyecto.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
VITE_FIREBASE_APP_ID=tu-app-id
```

Luego actualiza `src/config/app.js`:

```javascript
firebase: {
  enabled: true, // ← Cambiar a true
  config: {
    apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
    authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
    projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
    storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
    appId: import.meta.env.VITE_FIREBASE_APP_ID,
  },
}
```

#### **Opción B: Configuración Directa**

Edita directamente `src/config/app.js`:

```javascript
firebase: {
  enabled: true, // ← Cambiar a true
  config: {
    apiKey: "tu-api-key-real",
    authDomain: "tu-proyecto.firebaseapp.com",
    projectId: "tu-proyecto-id",
    storageBucket: "tu-proyecto.appspot.com",
    messagingSenderId: "123456789",
    appId: "tu-app-id",
  },
}
```

### **Paso 4: Configurar Firestore**

1. En Firebase Console, ve a **Firestore Database**
2. Haz clic en "Crear base de datos"
3. Selecciona "Comenzar en modo de prueba" (para desarrollo)
4. Elige una ubicación cercana a tus usuarios

### **Paso 5: Configurar Authentication**

1. En Firebase Console, ve a **Authentication**
2. En la pestaña "Sign-in method"
3. Habilita "Correo electrónico/contraseña"
4. Crea un usuario administrador:
   - Ve a la pestaña "Users"
   - Haz clic en "Agregar usuario"
   - Email: `<EMAIL>`
   - Contraseña: (elige una segura)

## 🧪 **Verificar Configuración**

1. Reinicia el servidor de desarrollo:
   ```bash
   npm run dev
   ```

2. Ve a la consola del navegador, deberías ver:
   ```
   ✅ Firebase inicializado correctamente
   ```

3. Intenta acceder al panel de administración:
   - Ve a `/admin`
   - Usa las credenciales que creaste

## 🔧 **Modo Desarrollo vs Producción**

### **Desarrollo (Firebase Deshabilitado)**
- ✅ Funciona con datos de muestra
- ✅ No requiere configuración
- ✅ Ideal para desarrollo y testing
- ❌ Los datos no se persisten

### **Producción (Firebase Habilitado)**
- ✅ Datos persistentes en la nube
- ✅ Autenticación real
- ✅ Escalable y seguro
- ❌ Requiere configuración inicial

## 🚨 **Solución de Problemas**

### **Error: "API key not valid"**
- Verifica que la API key sea correcta
- Asegúrate de que `firebase.enabled = true`
- Revisa que no haya espacios extra en las credenciales

### **Error: "Firebase project not found"**
- Verifica el `projectId`
- Asegúrate de que el proyecto existe en Firebase Console

### **Error: "Permission denied"**
- Configura las reglas de Firestore en modo de prueba
- Ve a Firestore → Rules y usa:
  ```javascript
  rules_version = '2';
  service cloud.firestore {
    match /databases/{database}/documents {
      match /{document=**} {
        allow read, write: if true;
      }
    }
  }
  ```

## 📚 **Recursos Adicionales**

- [Documentación de Firebase](https://firebase.google.com/docs)
- [Guía de Firestore](https://firebase.google.com/docs/firestore)
- [Firebase Authentication](https://firebase.google.com/docs/auth)

---

**¿Necesitas ayuda?** Revisa la documentación o crea un issue en el repositorio.
