<template>
  <!-- Modal Overlay with Teleport -->
  <teleport to="body">
    <transition name="cart-modal" appear @after-enter="focusModal" @after-leave="restoreBodyScroll">
      <div v-if="cartStore.isModalOpen"
        class="fixed inset-0 bg-black bg-opacity-50 z-[60] flex sm:justify-end justify-center sm:items-stretch items-center p-0 sm:p-4"
        @click="cartStore.closeModal()" @keydown.esc="cartStore.closeModal()">
        <!-- Modal Content -->
        <div ref="modalContent"
          class="bg-white w-full h-full sm:w-96 sm:h-auto sm:max-h-[95vh] sm:rounded-l-xl sm:rounded-r-none rounded-none shadow-xl overflow-hidden flex flex-col cart-content"
          @click.stop role="dialog" aria-modal="true" aria-labelledby="cart-title" aria-describedby="cart-description"
          tabindex="-1">
          <!-- Header -->
          <div class="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 id="cart-title" class="text-xl font-semibold text-gray-900">
              Tu Carrito ({{ cartStore.itemCount }})
            </h2>
            <button @click="cartStore.closeModal()"
              class="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200" aria-label="Cerrar carrito"
              title="Cerrar carrito">
              <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <!-- Cart Content -->
          <div class="flex-1 overflow-y-auto">
            <!-- Empty Cart State -->
            <div v-if="cartStore.items.length === 0" class="text-center py-12 px-6">
              <div class="text-6xl mb-4" role="img" aria-label="Carrito vacío">🛒</div>
              <h3 class="text-xl font-medium text-gray-900 mb-2">Tu carrito está vacío</h3>
              <p id="cart-description" class="text-gray-500 mb-6">¡Agrega algunos productos deliciosos!</p>
              <button @click="cartStore.closeModal()" class="btn-primary px-6 py-3"
                aria-label="Cerrar carrito e ir a la tienda">
                Ir a la Tienda
              </button>
            </div>

            <!-- Cart Items -->
            <div v-else class="p-6 space-y-4">
              <div v-for="item in cartStore.items" :key="item.id" class="p-4 bg-gray-50 rounded-lg">

                <!-- Product Header -->
                <div class="flex items-start space-x-4 mb-3">
                  <!-- Product Image -->
                  <div
                    class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden flex-shrink-0">
                    <img v-if="item.image" :src="item.image" :alt="item.name" class="w-full h-full object-cover"
                      @error="$event.target.style.display = 'none'" />
                    <span v-else class="text-2xl">{{ getCategoryEmoji(item.category) }}</span>
                  </div>

                  <!-- Product Info -->
                  <div class="flex-1 min-w-0">
                    <h4 class="text-base font-medium text-gray-900 leading-tight mb-1">{{ item.name }}</h4>
                    <p class="text-sm text-gray-500">{{ formatPrice(item.price) }}</p>
                  </div>

                  <!-- Remove Button -->
                  <button @click="cartStore.removeItem(item.id)"
                    class="p-2 text-red-400 hover:text-red-600 transition-colors duration-200 flex-shrink-0"
                    :aria-label="`Eliminar ${item.name} del carrito`" :title="`Eliminar ${item.name} del carrito`">
                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>

                <!-- Configuration Details -->
                <div v-if="item.configuration" class="bg-white rounded-lg p-3 mb-3 border border-gray-200">
                  <h5 class="text-sm font-medium text-gray-700 mb-2">📋 Configuración:</h5>
                  <div class="space-y-1 text-sm">
                    <div v-for="(value, key) in item.configuration" :key="key" class="flex justify-between">
                      <span class="text-gray-600 capitalize">{{ key }}:</span>
                      <span class="font-medium text-gray-900">{{ value }}</span>
                    </div>
                  </div>
                </div>

                <!-- Specifications -->
                <div v-if="item.specifications" class="bg-blue-50 rounded-lg p-3 mb-3 border border-blue-200">
                  <h5 class="text-sm font-medium text-blue-700 mb-2">ℹ️ Especificaciones:</h5>
                  <div class="space-y-1 text-sm">
                    <div v-for="(value, key) in item.specifications" :key="key" class="flex justify-between">
                      <span class="text-blue-600 capitalize">{{ key }}:</span>
                      <span class="font-medium text-blue-900">{{ value }}</span>
                    </div>
                  </div>
                </div>

                <!-- Quantity Controls -->
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <span class="text-sm text-gray-600">Cantidad:</span>
                    <div class="flex items-center space-x-2">
                      <button @click="cartStore.updateQuantity(item.id, item.quantity - 1)"
                        class="p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                        :disabled="item.quantity <= 1" :aria-label="`Disminuir cantidad de ${item.name}`"
                        :title="`Disminuir cantidad de ${item.name}`">
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
                        </svg>
                      </button>
                      <span class="w-8 text-center text-sm font-medium" :aria-label="`Cantidad: ${item.quantity}`">{{
                        item.quantity }}</span>
                      <button @click="cartStore.updateQuantity(item.id, item.quantity + 1)"
                        class="p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                        :aria-label="`Aumentar cantidad de ${item.name}`" :title="`Aumentar cantidad de ${item.name}`">
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                      </button>
                    </div>
                  </div>

                  <!-- Item Subtotal -->
                  <div class="text-right">
                    <span class="text-sm text-gray-600">Subtotal:</span>
                    <p class="text-lg font-semibold text-primary">{{ formatPrice(item.price * item.quantity) }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Footer with Total and Actions -->
            <div v-if="cartStore.items.length > 0" class="border-t border-gray-200 p-6 space-y-4">
              <!-- Total -->
              <div class="flex justify-between items-center">
                <span class="text-xl font-semibold text-gray-900">Total:</span>
                <span class="text-2xl font-bold text-primary">{{ cartStore.formattedTotal }}</span>
              </div>

              <!-- Action Buttons -->
              <div class="space-y-3">
                <button @click="handleCheckout"
                  class="w-full gradient-primary text-white py-4 px-6 rounded-lg text-lg font-medium hover:opacity-90 transition-all duration-200 flex items-center justify-center space-x-2"
                  aria-label="Proceder al checkout" title="Proceder al checkout">
                  <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9" />
                  </svg>
                  <span>Proceder al Checkout</span>
                </button>
                <button @click="cartStore.clearCart()"
                  class="w-full py-3 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                  aria-label="Vaciar todos los productos del carrito" title="Vaciar todos los productos del carrito">
                  Vaciar Carrito
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script setup>
import { ref, nextTick, onMounted, onUnmounted } from 'vue'
import { useCartStore } from '@/stores/cart'
import { useProductsStore } from '@/stores/products'
import { appConfig } from '@/config/app.js'

const cartStore = useCartStore()
const productsStore = useProductsStore()

const modalContent = ref(null)
let previousActiveElement = null

const formatPrice = (price) => productsStore.formatPrice(price)

const getCategoryEmoji = (category) => {
  const categoryConfig = appConfig.store.categories.find(cat => cat.id === category)
  return categoryConfig?.icon || '📦'
}

const handleCheckout = () => {
  // This can be customized based on the business needs
  // For now, we'll just close the modal and show an alert
  cartStore.closeModal()
  alert('Funcionalidad de checkout en desarrollo. ¡Pronto estará disponible!')

  // In a real implementation, this would:
  // 1. Open an order modal
  // 2. Redirect to a checkout page
  // 3. Integrate with payment systems
  // 4. Send order via WhatsApp/email
}

// Focus management
const focusModal = async () => {
  previousActiveElement = document.activeElement
  await nextTick()
  if (modalContent.value) {
    modalContent.value.focus()
  }
  lockBodyScroll()
}

const restoreBodyScroll = () => {
  unlockBodyScroll()
  if (previousActiveElement) {
    previousActiveElement.focus()
  }
}

// Body scroll lock
const lockBodyScroll = () => {
  document.body.style.overflow = 'hidden'
}

const unlockBodyScroll = () => {
  document.body.style.overflow = ''
}

// Focus trap
const handleTabKey = (e) => {
  if (!modalContent.value) return

  const focusableElements = modalContent.value.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  )

  const firstElement = focusableElements[0]
  const lastElement = focusableElements[focusableElements.length - 1]

  if (e.shiftKey) {
    if (document.activeElement === firstElement) {
      lastElement.focus()
      e.preventDefault()
    }
  } else {
    if (document.activeElement === lastElement) {
      firstElement.focus()
      e.preventDefault()
    }
  }
}

// Keyboard event handler
const handleKeydown = (e) => {
  if (e.key === 'Tab') {
    handleTabKey(e)
  } else if (e.key === 'Escape') {
    cartStore.closeModal()
  }
}

// Setup and cleanup
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  unlockBodyScroll()
})
</script>

<style scoped>
/* Cart Modal Animations */
.cart-modal-enter-active,
.cart-modal-leave-active {
  transition: all 0.3s ease;
}

.cart-modal-enter-from,
.cart-modal-leave-to {
  opacity: 0;
}

.cart-modal-enter-from .cart-content,
.cart-modal-leave-to .cart-content {
  transform: translateX(100%);
}

@media (max-width: 640px) {

  .cart-modal-enter-from .cart-content,
  .cart-modal-leave-to .cart-content {
    transform: translateY(100%);
  }
}

.cart-modal-enter-to .cart-content,
.cart-modal-leave-from .cart-content {
  transform: translateX(0);
}

@media (max-width: 640px) {

  .cart-modal-enter-to .cart-content,
  .cart-modal-leave-from .cart-content {
    transform: translateY(0);
  }
}

/* Custom scrollbar for cart items */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
