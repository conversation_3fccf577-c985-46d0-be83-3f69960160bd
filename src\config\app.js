/**
 * Configuración Principal de TiendaWeb
 *
 * Este archivo centraliza toda la configuración de la aplicación
 * para facilitar la personalización y adaptación a diferentes proyectos.
 */

export const appConfig = {
  // Información básica de la aplicación
  app: {
    name: "TiendaWeb",
    version: "1.0.0",
    description: "Plantilla modular de tienda web con Vue 3",
    author: "TiendaWeb Team",
    url: "https://tiendaweb.com",
  },

  // Información del negocio (configurable)
  business: {
    name: "Mi Tienda",
    description: "La mejor tienda online para tus necesidades",
    logo: "https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=100&h=100&fit=crop&crop=center",
    favicon: "/favicon.ico",

    // Información de contacto
    contact: {
      email: "<EMAIL>",
      phone: "+1234567890",
      whatsapp: "+1234567890",
      address: {
        street: "Calle Principal 123",
        city: "Ciudad",
        state: "Estado",
        country: "País",
        zipCode: "12345",
      },
    },

    // Redes sociales
    social: {
      facebook: "https://facebook.com/mitienda",
      instagram: "https://instagram.com/mitienda",
      twitter: "https://twitter.com/mitienda",
      youtube: "https://youtube.com/mitienda",
      tiktok: "https://tiktok.com/@mitienda",
    },

    // Horarios de atención
    hours: {
      monday: "9:00 AM - 6:00 PM",
      tuesday: "9:00 AM - 6:00 PM",
      wednesday: "9:00 AM - 6:00 PM",
      thursday: "9:00 AM - 6:00 PM",
      friday: "9:00 AM - 6:00 PM",
      saturday: "10:00 AM - 4:00 PM",
      sunday: "Cerrado",
    },
  },

  // Configuración de la tienda
  store: {
    // Moneda
    currency: {
      code: "USD",
      symbol: "$",
      position: "before", // 'before' o 'after'
      decimals: 2,
      thousandsSeparator: ",",
      decimalSeparator: ".",
    },

    // Categorías de productos
    categories: [
      { id: "electronics", name: "Electrónicos", icon: "📱" },
      { id: "clothing", name: "Ropa", icon: "👕" },
      { id: "home", name: "Hogar", icon: "🏠" },
      { id: "books", name: "Libros", icon: "📚" },
      { id: "sports", name: "Deportes", icon: "⚽" },
      { id: "beauty", name: "Belleza", icon: "💄" },
    ],

    // Configuración del carrito
    cart: {
      maxItems: 50,
      sessionTimeout: 24 * 60 * 60 * 1000, // 24 horas en ms
      saveToLocalStorage: true,
    },

    // Configuración de pedidos
    orders: {
      autoGenerateId: true,
      idPrefix: "ORD",
      statuses: [
        { id: "pending", name: "Pendiente", color: "warning" },
        { id: "confirmed", name: "Confirmado", color: "info" },
        { id: "processing", name: "Procesando", color: "primary" },
        { id: "shipped", name: "Enviado", color: "secondary" },
        { id: "delivered", name: "Entregado", color: "success" },
        { id: "cancelled", name: "Cancelado", color: "error" },
      ],
    },
  },

  // Configuración regional
  locale: {
    language: "es",
    country: "US",
    timezone: "America/New_York",
    dateFormat: "MM/DD/YYYY",
    timeFormat: "12h", // '12h' o '24h'
  },

  // Configuración de formularios
  forms: {
    // Validación de RUT (para países que lo usen)
    rutValidation: {
      enabled: false, // Cambiar a true para países como Chile
      format: "XX.XXX.XXX-X",
      countries: ["CL"], // Países que usan RUT
    },

    // Validación de teléfono
    phoneValidation: {
      enabled: true,
      format: "international", // 'national' o 'international'
      defaultCountry: "US",
    },

    // Campos requeridos en formularios
    requiredFields: {
      customer: ["name", "email", "phone"],
      order: ["customerInfo", "items"],
      product: ["name", "price", "category"],
    },
  },

  // Configuración de integración con WhatsApp
  whatsapp: {
    enabled: true,
    businessNumber: "+1234567890",
    messageTemplate: {
      newOrder:
        "🛒 *NUEVO PEDIDO*\n\n📋 *Detalles del pedido:*\n{orderDetails}\n\n👤 *Cliente:* {customerName}\n📧 *Email:* {customerEmail}\n📱 *Teléfono:* {customerPhone}\n\n💰 *Total:* {total}\n\n📅 *Fecha:* {date}\n⏰ *Hora:* {time}",
      orderConfirmation:
        "¡Hola {customerName}! 👋\n\nTu pedido #{orderId} ha sido confirmado.\n\n💰 Total: {total}\n\nTe contactaremos pronto para coordinar la entrega.\n\n¡Gracias por tu compra! 🙏",
    },
  },

  // Configuración de Firebase
  firebase: {
    // Estas configuraciones se deben llenar con los datos reales del proyecto
    config: {
      apiKey: "your-api-key",
      authDomain: "your-project.firebaseapp.com",
      projectId: "your-project-id",
      storageBucket: "your-project.appspot.com",
      messagingSenderId: "123456789",
      appId: "your-app-id",
    },

    // Colecciones de Firestore
    collections: {
      products: "products",
      customers: "customers",
      orders: "orders",
      categories: "categories",
      settings: "settings",
    },
  },

  // Configuración de la interfaz
  ui: {
    // Tema por defecto
    defaultTheme: "default", // 'default', 'warm', 'minimal'

    // Configuración de navegación
    navigation: {
      showLogo: true,
      showSearch: true,
      showCart: true,
      showUserMenu: false, // Para futuras funcionalidades de usuario
    },

    // Configuración de la página de inicio
    homepage: {
      showHero: true,
      showFeaturedProducts: true,
      showCategories: true,
      showTestimonials: false,
      showNewsletter: false,
    },

    // Configuración de productos
    products: {
      itemsPerPage: 12,
      showFilters: true,
      showSorting: true,
      showQuickView: true,
      imageAspectRatio: "1:1", // '1:1', '4:3', '16:9'
    },

    // Configuración del admin
    admin: {
      itemsPerPage: 20,
      showAdvancedFilters: true,
      enableBulkActions: true,
      showAnalytics: true,
    },
  },

  // Configuración de desarrollo
  development: {
    enableDebugMode: false,
    showPerformanceMetrics: false,
    enableHotReload: true,
    mockData: true, // Usar datos de ejemplo
  },
};

// Función para obtener configuración anidada
export function getConfig(path) {
  return path.split(".").reduce((obj, key) => obj?.[key], appConfig);
}

// Función para actualizar configuración
export function updateConfig(path, value) {
  const keys = path.split(".");
  const lastKey = keys.pop();
  const target = keys.reduce((obj, key) => obj[key], appConfig);
  target[lastKey] = value;
}

export default appConfig;
