/**
 * Configuración de Firebase
 *
 * Este archivo maneja la inicialización de Firebase de forma condicional
 * para evitar errores cuando no se tienen credenciales configuradas.
 */

import { appConfig } from "./app.js";

// Mock objects para desarrollo sin Firebase
const mockDb = {
  collection: () => ({
    getDocs: () => Promise.resolve({ empty: true, docs: [] }),
    addDoc: () => Promise.resolve({ id: "mock-id" }),
    doc: () => ({
      get: () => Promise.resolve({ exists: false }),
      set: () => Promise.resolve(),
      update: () => Promise.resolve(),
      delete: () => Promise.resolve(),
    }),
    query: () => ({
      getDocs: () => Promise.resolve({ empty: true, docs: [] }),
    }),
    orderBy: () => ({
      getDocs: () => Promise.resolve({ empty: true, docs: [] }),
    }),
    where: () => ({
      getDocs: () => Promise.resolve({ empty: true, docs: [] }),
    }),
  }),
};

const mockAuth = {
  currentUser: null,
  signInWithEmailAndPassword: () =>
    Promise.resolve({ user: { uid: "mock-user", email: "<EMAIL>" } }),
  signOut: () => Promise.resolve(),
  onAuthStateChanged: (callback) => {
    // Simular usuario no autenticado
    setTimeout(() => callback(null), 100);
    return () => {}; // unsubscribe function
  },
};

// Exportar mocks por defecto
export let db = mockDb;
export let auth = mockAuth;

// Función para inicializar Firebase de forma asíncrona
async function initializeFirebase() {
  if (appConfig.firebase.enabled && appConfig.firebase.config.apiKey !== "your-api-key") {
    try {
      // Importar Firebase dinámicamente
      const { initializeApp } = await import("firebase/app");
      const { getFirestore } = await import("firebase/firestore");
      const { getAuth } = await import("firebase/auth");

      // Initialize Firebase
      const firebaseApp = initializeApp(appConfig.firebase.config);
      db = getFirestore(firebaseApp);
      auth = getAuth(firebaseApp);

      console.log("✅ Firebase inicializado correctamente");
    } catch (error) {
      console.warn("⚠️ Error inicializando Firebase, usando mocks:", error.message);
      // Mantener los mocks en caso de error
    }
  } else {
    console.log("🔧 Firebase deshabilitado, usando datos de desarrollo");
  }
}

// Inicializar Firebase cuando se importe el módulo
initializeFirebase();
