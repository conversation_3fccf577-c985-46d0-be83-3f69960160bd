/**
 * Sistema de Temas Configurable para TiendaWeb
 * 
 * Este archivo permite cambiar fácilmente los colores, fuentes y estilos
 * de toda la aplicación para adaptarla a diferentes marcas y proyectos.
 */

// Tema por defecto - Neutro y profesional
export const defaultTheme = {
  name: 'default',
  colors: {
    // Colores principales
    primary: '#3b82f6',        // Azul moderno
    primaryDark: '#1d4ed8',    // Azul oscuro
    primaryLight: '#93c5fd',   // Azul claro
    
    secondary: '#6366f1',      // Índigo
    secondaryDark: '#4338ca',  // Índigo oscuro
    secondaryLight: '#a5b4fc', // Índigo claro
    
    accent: '#10b981',         // Verde esmeralda
    accentDark: '#047857',     // Verde oscuro
    accentLight: '#6ee7b7',    // Verde claro
    
    // Colores neutros
    neutral: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827',
    },
    
    // Estados
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
  },
  
  fonts: {
    sans: 'Inter, system-ui, sans-serif',
    display: 'Inter, system-ui, sans-serif',
    mono: 'JetBrains Mono, monospace',
  },
  
  spacing: {
    containerMaxWidth: '1200px',
    sectionPadding: '4rem',
    cardPadding: '1.5rem',
  },
  
  borderRadius: {
    small: '0.375rem',
    medium: '0.5rem',
    large: '0.75rem',
    xl: '1rem',
  },
  
  shadows: {
    card: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    cardHover: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    modal: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  }
}

// Tema alternativo - Cálido y acogedor
export const warmTheme = {
  name: 'warm',
  colors: {
    primary: '#f59e0b',        // Ámbar
    primaryDark: '#d97706',    
    primaryLight: '#fbbf24',   
    
    secondary: '#ef4444',      // Rojo
    secondaryDark: '#dc2626',  
    secondaryLight: '#f87171', 
    
    accent: '#8b5cf6',         // Violeta
    accentDark: '#7c3aed',     
    accentLight: '#a78bfa',    
    
    neutral: {
      50: '#fefdf8',
      100: '#fef7ed',
      200: '#fed7aa',
      300: '#fdba74',
      400: '#fb923c',
      500: '#f97316',
      600: '#ea580c',
      700: '#c2410c',
      800: '#9a3412',
      900: '#7c2d12',
    },
    
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
  },
  
  fonts: {
    sans: 'Poppins, system-ui, sans-serif',
    display: 'Poppins, system-ui, sans-serif',
    mono: 'Fira Code, monospace',
  },
  
  spacing: {
    containerMaxWidth: '1200px',
    sectionPadding: '5rem',
    cardPadding: '2rem',
  },
  
  borderRadius: {
    small: '0.5rem',
    medium: '0.75rem',
    large: '1rem',
    xl: '1.5rem',
  },
  
  shadows: {
    card: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    cardHover: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    modal: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  }
}

// Tema minimalista - Limpio y elegante
export const minimalTheme = {
  name: 'minimal',
  colors: {
    primary: '#000000',        // Negro
    primaryDark: '#000000',    
    primaryLight: '#374151',   
    
    secondary: '#6b7280',      // Gris
    secondaryDark: '#4b5563',  
    secondaryLight: '#9ca3af', 
    
    accent: '#3b82f6',         // Azul como único color
    accentDark: '#1d4ed8',     
    accentLight: '#93c5fd',    
    
    neutral: {
      50: '#ffffff',
      100: '#f9fafb',
      200: '#f3f4f6',
      300: '#e5e7eb',
      400: '#d1d5db',
      500: '#9ca3af',
      600: '#6b7280',
      700: '#4b5563',
      800: '#374151',
      900: '#000000',
    },
    
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
  },
  
  fonts: {
    sans: 'Inter, system-ui, sans-serif',
    display: 'Inter, system-ui, sans-serif',
    mono: 'SF Mono, monospace',
  },
  
  spacing: {
    containerMaxWidth: '1000px',
    sectionPadding: '3rem',
    cardPadding: '1rem',
  },
  
  borderRadius: {
    small: '0.25rem',
    medium: '0.375rem',
    large: '0.5rem',
    xl: '0.75rem',
  },
  
  shadows: {
    card: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    cardHover: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    modal: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  }
}

// Función para aplicar un tema
export function applyTheme(theme) {
  const root = document.documentElement
  
  // Aplicar colores
  root.style.setProperty('--color-primary', theme.colors.primary)
  root.style.setProperty('--color-primary-dark', theme.colors.primaryDark)
  root.style.setProperty('--color-primary-light', theme.colors.primaryLight)
  
  root.style.setProperty('--color-secondary', theme.colors.secondary)
  root.style.setProperty('--color-secondary-dark', theme.colors.secondaryDark)
  root.style.setProperty('--color-secondary-light', theme.colors.secondaryLight)
  
  root.style.setProperty('--color-accent', theme.colors.accent)
  root.style.setProperty('--color-accent-dark', theme.colors.accentDark)
  root.style.setProperty('--color-accent-light', theme.colors.accentLight)
  
  // Aplicar colores neutros
  Object.entries(theme.colors.neutral).forEach(([key, value]) => {
    root.style.setProperty(`--color-neutral-${key}`, value)
  })
  
  // Aplicar colores de estado
  root.style.setProperty('--color-success', theme.colors.success)
  root.style.setProperty('--color-warning', theme.colors.warning)
  root.style.setProperty('--color-error', theme.colors.error)
  root.style.setProperty('--color-info', theme.colors.info)
  
  // Aplicar fuentes
  root.style.setProperty('--font-sans', theme.fonts.sans)
  root.style.setProperty('--font-display', theme.fonts.display)
  root.style.setProperty('--font-mono', theme.fonts.mono)
}

// Temas disponibles
export const themes = {
  default: defaultTheme,
  warm: warmTheme,
  minimal: minimalTheme,
}

// Tema actual (por defecto)
export let currentTheme = defaultTheme
