/**
 * Datos de ejemplo para clientes
 * 
 * Estos datos se usan para demostración y testing.
 * En producción, los datos vendrán de Firebase/Firestore.
 */

export const sampleCustomers = [
  {
    id: 'cust-001',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+1234567890',
    rut: '12.345.678-9', // Ejemplo para países que usan RUT
    birthDate: '1985-03-15',
    address: {
      street: 'Calle Principal 123',
      city: 'Ciudad',
      state: 'Estado',
      country: 'País',
      zipCode: '12345'
    },
    social: {
      instagram: '@maria_gonzalez',
      facebook: 'https://facebook.com/maria.gonzalez'
    },
    preferences: {
      newsletter: true,
      smsNotifications: false,
      emailNotifications: true
    },
    stats: {
      totalOrders: 15,
      totalSpent: 1250.75,
      averageOrderValue: 83.38,
      lastOrderDate: new Date('2024-01-18')
    },
    tags: ['vip', 'frequent-buyer'],
    notes: 'Cliente preferencial, siempre puntual en pagos',
    createdAt: new Date('2023-06-15'),
    updatedAt: new Date('2024-01-18')
  },
  {
    id: 'cust-002',
    name: '<PERSON> <PERSON>',
    email: '<EMAIL>',
    phone: '+1234567891',
    rut: '98.765.432-1',
    birthDate: '1990-07-22',
    address: {
      street: 'Avenida Central 456',
      city: 'Ciudad',
      state: 'Estado',
      country: 'País',
      zipCode: '12346'
    },
    social: {
      instagram: '@carlos_rod',
      facebook: null
    },
    preferences: {
      newsletter: false,
      smsNotifications: true,
      emailNotifications: true
    },
    stats: {
      totalOrders: 8,
      totalSpent: 650.25,
      averageOrderValue: 81.28,
      lastOrderDate: new Date('2024-01-15')
    },
    tags: ['regular'],
    notes: 'Prefiere productos electrónicos',
    createdAt: new Date('2023-09-10'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: 'cust-003',
    name: 'Ana Martínez',
    email: '<EMAIL>',
    phone: '+1234567892',
    rut: '11.222.333-4',
    birthDate: '1988-12-03',
    address: {
      street: 'Plaza Mayor 789',
      city: 'Ciudad',
      state: 'Estado',
      country: 'País',
      zipCode: '12347'
    },
    social: {
      instagram: '@ana_martinez_style',
      facebook: 'https://facebook.com/ana.martinez'
    },
    preferences: {
      newsletter: true,
      smsNotifications: true,
      emailNotifications: true
    },
    stats: {
      totalOrders: 22,
      totalSpent: 1890.50,
      averageOrderValue: 85.93,
      lastOrderDate: new Date('2024-01-20')
    },
    tags: ['vip', 'influencer', 'fashion-lover'],
    notes: 'Influencer de moda, comparte productos en redes sociales',
    createdAt: new Date('2023-03-20'),
    updatedAt: new Date('2024-01-20')
  },
  {
    id: 'cust-004',
    name: 'Luis Fernández',
    email: '<EMAIL>',
    phone: '+1234567893',
    rut: '55.666.777-8',
    birthDate: '1992-05-18',
    address: {
      street: 'Calle Comercio 321',
      city: 'Ciudad',
      state: 'Estado',
      country: 'País',
      zipCode: '12348'
    },
    social: {
      instagram: null,
      facebook: null
    },
    preferences: {
      newsletter: false,
      smsNotifications: false,
      emailNotifications: true
    },
    stats: {
      totalOrders: 3,
      totalSpent: 180.75,
      averageOrderValue: 60.25,
      lastOrderDate: new Date('2024-01-10')
    },
    tags: ['new-customer'],
    notes: 'Cliente nuevo, interesado en productos para el hogar',
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-10')
  },
  {
    id: 'cust-005',
    name: 'Patricia Silva',
    email: '<EMAIL>',
    phone: '+1234567894',
    rut: '77.888.999-0',
    birthDate: '1983-11-30',
    address: {
      street: 'Barrio Norte 654',
      city: 'Ciudad',
      state: 'Estado',
      country: 'País',
      zipCode: '12349'
    },
    social: {
      instagram: '@patty_silva',
      facebook: 'https://facebook.com/patricia.silva'
    },
    preferences: {
      newsletter: true,
      smsNotifications: true,
      emailNotifications: true
    },
    stats: {
      totalOrders: 12,
      totalSpent: 980.25,
      averageOrderValue: 81.69,
      lastOrderDate: new Date('2024-01-17')
    },
    tags: ['loyal-customer', 'book-lover'],
    notes: 'Compra frecuentemente libros y productos de belleza',
    createdAt: new Date('2023-08-12'),
    updatedAt: new Date('2024-01-17')
  },
  {
    id: 'cust-006',
    name: 'Roberto Morales',
    email: '<EMAIL>',
    phone: '+1234567895',
    rut: '33.444.555-6',
    birthDate: '1987-09-14',
    address: {
      street: 'Zona Industrial 987',
      city: 'Ciudad',
      state: 'Estado',
      country: 'País',
      zipCode: '12350'
    },
    social: {
      instagram: '@roberto_fitness',
      facebook: null
    },
    preferences: {
      newsletter: false,
      smsNotifications: true,
      emailNotifications: false
    },
    stats: {
      totalOrders: 18,
      totalSpent: 1420.80,
      averageOrderValue: 78.93,
      lastOrderDate: new Date('2024-01-19')
    },
    tags: ['sports-enthusiast', 'tech-savvy'],
    notes: 'Interesado en productos deportivos y tecnología',
    createdAt: new Date('2023-05-08'),
    updatedAt: new Date('2024-01-19')
  }
]

// Función para obtener clientes VIP
export function getVipCustomers() {
  return sampleCustomers.filter(customer => customer.tags.includes('vip'))
}

// Función para obtener clientes por tag
export function getCustomersByTag(tag) {
  return sampleCustomers.filter(customer => customer.tags.includes(tag))
}

// Función para buscar clientes
export function searchCustomers(query) {
  const searchTerm = query.toLowerCase()
  return sampleCustomers.filter(customer => 
    customer.name.toLowerCase().includes(searchTerm) ||
    customer.email.toLowerCase().includes(searchTerm) ||
    customer.phone.includes(searchTerm) ||
    (customer.rut && customer.rut.includes(searchTerm))
  )
}

// Función para obtener cliente por ID
export function getCustomerById(id) {
  return sampleCustomers.find(customer => customer.id === id)
}

// Función para obtener estadísticas de clientes
export function getCustomerStats() {
  const totalCustomers = sampleCustomers.length
  const vipCustomers = getVipCustomers().length
  const totalSpent = sampleCustomers.reduce((sum, customer) => sum + customer.stats.totalSpent, 0)
  const averageSpent = totalSpent / totalCustomers
  const totalOrders = sampleCustomers.reduce((sum, customer) => sum + customer.stats.totalOrders, 0)

  return {
    totalCustomers,
    vipCustomers,
    totalSpent,
    averageSpent,
    totalOrders,
    averageOrdersPerCustomer: totalOrders / totalCustomers
  }
}

// Función para obtener clientes recientes
export function getRecentCustomers(days = 30) {
  const cutoffDate = new Date()
  cutoffDate.setDate(cutoffDate.getDate() - days)
  
  return sampleCustomers.filter(customer => 
    new Date(customer.createdAt) >= cutoffDate
  )
}
