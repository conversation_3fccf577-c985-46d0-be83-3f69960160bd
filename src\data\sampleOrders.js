/**
 * Datos de ejemplo para pedidos/ventas
 * 
 * Estos datos se usan para demostración y testing.
 * En producción, los datos vendrán de Firebase/Firestore.
 */

export const sampleOrders = [
  {
    id: 'ORD-001',
    customerId: 'cust-001',
    customerInfo: {
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+1234567890'
    },
    items: [
      {
        id: 'prod-001',
        name: 'Smartphone Pro Max',
        price: 899.99,
        quantity: 1,
        category: 'electronics'
      },
      {
        id: 'prod-003',
        name: 'Auriculares Inalámbricos',
        price: 199.99,
        quantity: 1,
        category: 'electronics'
      }
    ],
    subtotal: 1099.98,
    tax: 87.99,
    shipping: 15.00,
    total: 1202.97,
    status: 'delivered',
    paymentMethod: 'credit_card',
    paymentStatus: 'paid',
    shippingAddress: {
      street: 'Calle Principal 123',
      city: 'Ciudad',
      state: 'Estado',
      country: 'País',
      zipCode: '12345'
    },
    trackingNumber: 'TRK123456789',
    notes: 'Entrega en horario de oficina',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-18'),
    deliveredAt: new Date('2024-01-18')
  },
  {
    id: 'ORD-002',
    customerId: 'cust-002',
    customerInfo: {
      name: 'Carlos Rodríguez',
      email: '<EMAIL>',
      phone: '+**********'
    },
    items: [
      {
        id: 'prod-002',
        name: 'Laptop Gaming RGB',
        price: 1299.99,
        quantity: 1,
        category: 'electronics'
      }
    ],
    subtotal: 1299.99,
    tax: 103.99,
    shipping: 0.00, // Envío gratis
    total: 1403.98,
    status: 'shipped',
    paymentMethod: 'bank_transfer',
    paymentStatus: 'paid',
    shippingAddress: {
      street: 'Avenida Central 456',
      city: 'Ciudad',
      state: 'Estado',
      country: 'País',
      zipCode: '12346'
    },
    trackingNumber: 'TRK987654321',
    notes: 'Cliente solicitó empaque especial',
    createdAt: new Date('2024-01-12'),
    updatedAt: new Date('2024-01-16'),
    shippedAt: new Date('2024-01-16')
  },
  {
    id: 'ORD-003',
    customerId: 'cust-003',
    customerInfo: {
      name: 'Ana Martínez',
      email: '<EMAIL>',
      phone: '+**********'
    },
    items: [
      {
        id: 'prod-004',
        name: 'Camiseta Casual Premium',
        price: 29.99,
        quantity: 3,
        category: 'clothing'
      },
      {
        id: 'prod-005',
        name: 'Jeans Clásicos',
        price: 79.99,
        quantity: 2,
        category: 'clothing'
      },
      {
        id: 'prod-010',
        name: 'Set de Maquillaje Profesional',
        price: 89.99,
        quantity: 1,
        category: 'beauty'
      }
    ],
    subtotal: 339.95,
    tax: 27.20,
    shipping: 12.00,
    total: 379.15,
    status: 'processing',
    paymentMethod: 'credit_card',
    paymentStatus: 'paid',
    shippingAddress: {
      street: 'Plaza Mayor 789',
      city: 'Ciudad',
      state: 'Estado',
      country: 'País',
      zipCode: '12347'
    },
    trackingNumber: null,
    notes: 'Cliente influencer - incluir material promocional',
    createdAt: new Date('2024-01-18'),
    updatedAt: new Date('2024-01-19')
  },
  {
    id: 'ORD-004',
    customerId: 'cust-004',
    customerInfo: {
      name: 'Luis Fernández',
      email: '<EMAIL>',
      phone: '+1234567893'
    },
    items: [
      {
        id: 'prod-006',
        name: 'Lámpara LED Inteligente',
        price: 49.99,
        quantity: 2,
        category: 'home'
      },
      {
        id: 'prod-007',
        name: 'Cojín Decorativo',
        price: 24.99,
        quantity: 4,
        category: 'home'
      }
    ],
    subtotal: 199.94,
    tax: 15.99,
    shipping: 8.00,
    total: 223.93,
    status: 'confirmed',
    paymentMethod: 'paypal',
    paymentStatus: 'paid',
    shippingAddress: {
      street: 'Calle Comercio 321',
      city: 'Ciudad',
      state: 'Estado',
      country: 'País',
      zipCode: '12348'
    },
    trackingNumber: null,
    notes: 'Primera compra del cliente',
    createdAt: new Date('2024-01-19'),
    updatedAt: new Date('2024-01-19')
  },
  {
    id: 'ORD-005',
    customerId: 'cust-005',
    customerInfo: {
      name: 'Patricia Silva',
      email: '<EMAIL>',
      phone: '+**********'
    },
    items: [
      {
        id: 'prod-008',
        name: 'Guía de Programación Web',
        price: 39.99,
        quantity: 2,
        category: 'books'
      }
    ],
    subtotal: 79.98,
    tax: 6.40,
    shipping: 5.00,
    total: 91.38,
    status: 'pending',
    paymentMethod: 'bank_transfer',
    paymentStatus: 'pending',
    shippingAddress: {
      street: 'Barrio Norte 654',
      city: 'Ciudad',
      state: 'Estado',
      country: 'País',
      zipCode: '12349'
    },
    trackingNumber: null,
    notes: 'Esperando confirmación de pago',
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-20')
  },
  {
    id: 'ORD-006',
    customerId: 'cust-006',
    customerInfo: {
      name: 'Roberto Morales',
      email: '<EMAIL>',
      phone: '+**********'
    },
    items: [
      {
        id: 'prod-009',
        name: 'Pelota de Fútbol Profesional',
        price: 59.99,
        quantity: 1,
        category: 'sports'
      },
      {
        id: 'prod-001',
        name: 'Smartphone Pro Max',
        price: 899.99,
        quantity: 1,
        category: 'electronics'
      }
    ],
    subtotal: 959.98,
    tax: 76.80,
    shipping: 10.00,
    total: 1046.78,
    status: 'cancelled',
    paymentMethod: 'credit_card',
    paymentStatus: 'refunded',
    shippingAddress: {
      street: 'Zona Industrial 987',
      city: 'Ciudad',
      state: 'Estado',
      country: 'País',
      zipCode: '12350'
    },
    trackingNumber: null,
    notes: 'Cliente canceló por cambio de planes',
    createdAt: new Date('2024-01-14'),
    updatedAt: new Date('2024-01-16'),
    cancelledAt: new Date('2024-01-16')
  }
]

// Función para obtener pedidos por estado
export function getOrdersByStatus(status) {
  return sampleOrders.filter(order => order.status === status)
}

// Función para obtener pedidos por cliente
export function getOrdersByCustomer(customerId) {
  return sampleOrders.filter(order => order.customerId === customerId)
}

// Función para buscar pedidos
export function searchOrders(query) {
  const searchTerm = query.toLowerCase()
  return sampleOrders.filter(order => 
    order.id.toLowerCase().includes(searchTerm) ||
    order.customerInfo.name.toLowerCase().includes(searchTerm) ||
    order.customerInfo.email.toLowerCase().includes(searchTerm) ||
    order.trackingNumber?.toLowerCase().includes(searchTerm)
  )
}

// Función para obtener pedido por ID
export function getOrderById(id) {
  return sampleOrders.find(order => order.id === id)
}

// Función para obtener estadísticas de ventas
export function getSalesStats() {
  const totalOrders = sampleOrders.length
  const totalRevenue = sampleOrders
    .filter(order => order.paymentStatus === 'paid')
    .reduce((sum, order) => sum + order.total, 0)
  
  const ordersByStatus = {
    pending: getOrdersByStatus('pending').length,
    confirmed: getOrdersByStatus('confirmed').length,
    processing: getOrdersByStatus('processing').length,
    shipped: getOrdersByStatus('shipped').length,
    delivered: getOrdersByStatus('delivered').length,
    cancelled: getOrdersByStatus('cancelled').length
  }

  const averageOrderValue = totalRevenue / totalOrders
  
  return {
    totalOrders,
    totalRevenue,
    averageOrderValue,
    ordersByStatus
  }
}

// Función para obtener pedidos recientes
export function getRecentOrders(days = 7) {
  const cutoffDate = new Date()
  cutoffDate.setDate(cutoffDate.getDate() - days)
  
  return sampleOrders.filter(order => 
    new Date(order.createdAt) >= cutoffDate
  ).sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
}

// Función para obtener ventas por período
export function getSalesByPeriod(startDate, endDate) {
  return sampleOrders.filter(order => {
    const orderDate = new Date(order.createdAt)
    return orderDate >= startDate && orderDate <= endDate
  })
}
