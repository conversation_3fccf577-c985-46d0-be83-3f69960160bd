/**
 * Datos de ejemplo para productos de la tienda
 * 
 * Estos datos se usan para demostración y testing.
 * En producción, los datos vendrán de Firebase/Firestore.
 */

export const sampleProducts = [
  // Electrónicos
  {
    id: 'prod-001',
    name: 'Smartphone Pro Max',
    description: 'Smartphone de última generación con cámara profesional y batería de larga duración.',
    price: 899.99,
    category: 'electronics',
    imageUrl: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=400&fit=crop',
    inStock: true,
    stock: 25,
    featured: true,
    tags: ['smartphone', 'tecnología', 'cámara'],
    specifications: {
      brand: 'TechBrand',
      model: 'Pro Max 2024',
      color: 'Negro',
      storage: '256GB',
      warranty: '2 años'
    },
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: 'prod-002',
    name: 'Laptop Gaming RGB',
    description: 'Laptop para gaming con procesador de alta gama y tarjeta gráfica dedicada.',
    price: 1299.99,
    category: 'electronics',
    imageUrl: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=400&fit=crop',
    inStock: true,
    stock: 12,
    featured: true,
    tags: ['laptop', 'gaming', 'RGB'],
    specifications: {
      brand: 'GameTech',
      processor: 'Intel i7',
      ram: '16GB',
      storage: '1TB SSD',
      graphics: 'RTX 4060'
    },
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-10')
  },
  {
    id: 'prod-003',
    name: 'Auriculares Inalámbricos',
    description: 'Auriculares con cancelación de ruido y sonido de alta calidad.',
    price: 199.99,
    category: 'electronics',
    imageUrl: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
    inStock: true,
    stock: 45,
    featured: false,
    tags: ['auriculares', 'inalámbrico', 'música'],
    specifications: {
      brand: 'AudioPro',
      type: 'Over-ear',
      battery: '30 horas',
      connectivity: 'Bluetooth 5.0'
    },
    createdAt: new Date('2024-01-08'),
    updatedAt: new Date('2024-01-08')
  },

  // Ropa
  {
    id: 'prod-004',
    name: 'Camiseta Casual Premium',
    description: 'Camiseta de algodón 100% orgánico, cómoda y duradera.',
    price: 29.99,
    category: 'clothing',
    imageUrl: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop',
    inStock: true,
    stock: 100,
    featured: false,
    tags: ['camiseta', 'algodón', 'casual'],
    specifications: {
      brand: 'EcoWear',
      material: '100% Algodón Orgánico',
      sizes: ['S', 'M', 'L', 'XL'],
      colors: ['Blanco', 'Negro', 'Azul', 'Gris']
    },
    createdAt: new Date('2024-01-12'),
    updatedAt: new Date('2024-01-12')
  },
  {
    id: 'prod-005',
    name: 'Jeans Clásicos',
    description: 'Jeans de corte clásico con ajuste perfecto y máxima comodidad.',
    price: 79.99,
    category: 'clothing',
    imageUrl: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=400&h=400&fit=crop',
    inStock: true,
    stock: 60,
    featured: true,
    tags: ['jeans', 'denim', 'clásico'],
    specifications: {
      brand: 'DenimCo',
      fit: 'Regular',
      material: '98% Algodón, 2% Elastano',
      sizes: ['28', '30', '32', '34', '36', '38']
    },
    createdAt: new Date('2024-01-14'),
    updatedAt: new Date('2024-01-14')
  },

  // Hogar
  {
    id: 'prod-006',
    name: 'Lámpara LED Inteligente',
    description: 'Lámpara LED con control por app y múltiples colores.',
    price: 49.99,
    category: 'home',
    imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop',
    inStock: true,
    stock: 30,
    featured: false,
    tags: ['lámpara', 'LED', 'inteligente'],
    specifications: {
      brand: 'SmartHome',
      power: '12W',
      colors: '16 millones',
      connectivity: 'WiFi',
      compatibility: 'Alexa, Google Home'
    },
    createdAt: new Date('2024-01-11'),
    updatedAt: new Date('2024-01-11')
  },
  {
    id: 'prod-007',
    name: 'Cojín Decorativo',
    description: 'Cojín suave y decorativo para sala o dormitorio.',
    price: 24.99,
    category: 'home',
    imageUrl: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop',
    inStock: true,
    stock: 80,
    featured: false,
    tags: ['cojín', 'decoración', 'hogar'],
    specifications: {
      brand: 'HomeDecor',
      size: '45x45cm',
      material: 'Algodón y Poliéster',
      colors: ['Beige', 'Azul', 'Verde', 'Rosa']
    },
    createdAt: new Date('2024-01-09'),
    updatedAt: new Date('2024-01-09')
  },

  // Libros
  {
    id: 'prod-008',
    name: 'Guía de Programación Web',
    description: 'Libro completo sobre desarrollo web moderno con Vue.js y Node.js.',
    price: 39.99,
    category: 'books',
    imageUrl: 'https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?w=400&h=400&fit=crop',
    inStock: true,
    stock: 50,
    featured: true,
    tags: ['programación', 'web', 'Vue.js'],
    specifications: {
      author: 'Tech Expert',
      pages: '450',
      language: 'Español',
      publisher: 'TechBooks',
      isbn: '978-1234567890'
    },
    createdAt: new Date('2024-01-13'),
    updatedAt: new Date('2024-01-13')
  },

  // Deportes
  {
    id: 'prod-009',
    name: 'Pelota de Fútbol Profesional',
    description: 'Pelota oficial para fútbol profesional, certificada FIFA.',
    price: 59.99,
    category: 'sports',
    imageUrl: 'https://images.unsplash.com/photo-1614632537190-23e4b2e69c88?w=400&h=400&fit=crop',
    inStock: true,
    stock: 35,
    featured: false,
    tags: ['fútbol', 'pelota', 'deportes'],
    specifications: {
      brand: 'SportsPro',
      size: 'Oficial',
      material: 'Cuero sintético',
      certification: 'FIFA Approved'
    },
    createdAt: new Date('2024-01-07'),
    updatedAt: new Date('2024-01-07')
  },

  // Belleza
  {
    id: 'prod-010',
    name: 'Set de Maquillaje Profesional',
    description: 'Kit completo de maquillaje con brochas y productos de alta calidad.',
    price: 89.99,
    category: 'beauty',
    imageUrl: 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=400&fit=crop',
    inStock: true,
    stock: 20,
    featured: true,
    tags: ['maquillaje', 'belleza', 'profesional'],
    specifications: {
      brand: 'BeautyPro',
      items: '25 productos',
      includes: 'Brochas, sombras, labiales, base',
      crueltyFree: true
    },
    createdAt: new Date('2024-01-06'),
    updatedAt: new Date('2024-01-06')
  }
]

// Función para obtener productos por categoría
export function getProductsByCategory(categoryId) {
  return sampleProducts.filter(product => product.category === categoryId)
}

// Función para obtener productos destacados
export function getFeaturedProducts() {
  return sampleProducts.filter(product => product.featured)
}

// Función para buscar productos
export function searchProducts(query) {
  const searchTerm = query.toLowerCase()
  return sampleProducts.filter(product => 
    product.name.toLowerCase().includes(searchTerm) ||
    product.description.toLowerCase().includes(searchTerm) ||
    product.tags.some(tag => tag.toLowerCase().includes(searchTerm))
  )
}

// Función para obtener producto por ID
export function getProductById(id) {
  return sampleProducts.find(product => product.id === id)
}
