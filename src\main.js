import { createApp } from "vue";
import { createPinia } from "pinia";
import App from "./App.vue";
import router from "./router";
import "./style.css";

// Configuración de la aplicación
import { appConfig } from "./config/app.js";
import { applyTheme, themes } from "./config/theme.js";

// Firebase - Importar configuración
export { db, auth } from "./config/firebase.js";

// Aplicar tema por defecto
const defaultTheme = themes[appConfig.ui.defaultTheme] || themes.default;
applyTheme(defaultTheme);

// Crear aplicación Vue
const app = createApp(App);
const pinia = createPinia();

// Configurar plugins
app.use(pinia);
app.use(router);

// Proporcionar configuración global
app.provide("appConfig", appConfig);

// Montar aplicación
app.mount("#app");

// Inicializar stores después del montaje
import { useAuthStore } from "./stores/auth";
import { useProductsStore } from "./stores/products";

const authStore = useAuthStore();
const productsStore = useProductsStore();

authStore.initializeAuth();
productsStore.fetchProducts(); // Cargar productos al iniciar la app
