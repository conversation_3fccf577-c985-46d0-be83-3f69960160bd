import { createApp } from "vue";
import { createPinia } from "pinia";
import App from "./App.vue";
import router from "./router";
import "./style.css";

// Configuración de la aplicación
import { appConfig } from "./config/app.js";
import { applyTheme, themes } from "./config/theme.js";

// Firebase
import { initializeApp } from "firebase/app";
import { getFirestore } from "firebase/firestore";
import { getAuth } from "firebase/auth";

// Firebase config - Usar configuración desde appConfig
const firebaseConfig = appConfig.firebase.config;

// Initialize Firebase
const firebaseApp = initializeApp(firebaseConfig);
export const db = getFirestore(firebaseApp);
export const auth = getAuth(firebaseApp);

// Aplicar tema por defecto
const defaultTheme = themes[appConfig.ui.defaultTheme] || themes.default;
applyTheme(defaultTheme);

// Crear aplicación Vue
const app = createApp(App);
const pinia = createPinia();

// Configurar plugins
app.use(pinia);
app.use(router);

// Proporcionar configuración global
app.provide("appConfig", appConfig);

// Montar aplicación
app.mount("#app");

// Inicializar stores después del montaje
import { useAuthStore } from "./stores/auth";
import { useProductsStore } from "./stores/products";

const authStore = useAuthStore();
const productsStore = useProductsStore();

authStore.initializeAuth();
productsStore.fetchProducts(); // Cargar productos al iniciar la app
