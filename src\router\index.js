import { createRouter, createWebHistory } from "vue-router";
import { appConfig } from "@/config/app.js";

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    // Rutas públicas
    {
      path: "/",
      name: "home",
      component: () => import("@/views/HomeView.vue"),
      meta: {
        title: `Inicio - ${appConfig.business.name}`,
        description: appConfig.business.description,
        public: true,
      },
    },
    {
      path: "/tienda",
      name: "tienda",
      component: () => import("@/views/TiendaView.vue"),
      meta: {
        title: `Tienda - ${appConfig.business.name}`,
        description: "Explora nuestra variedad de productos",
        public: true,
      },
    },
    {
      path: "/productos",
      name: "productos",
      redirect: "/tienda",
    },
    {
      path: "/contacto",
      name: "contacto",
      component: () => import("@/views/ContactView.vue"),
      meta: {
        title: `Contacto - ${appConfig.business.name}`,
        description: "Contáctanos para pedidos personalizados o consultas",
        public: true,
      },
    },

    // Rutas de autenticación
    {
      path: "/login",
      name: "login",
      component: () => import("@/views/auth/LoginView.vue"),
      meta: {
        title: `Iniciar Sesión - ${appConfig.business.name}`,
        public: true,
        hideForAuth: true, // Ocultar si ya está autenticado
      },
    },

    // Rutas de administración
    {
      path: "/admin",
      name: "admin",
      component: () => import("@/views/admin/AdminView.vue"),
      meta: {
        title: "Panel de Administración",
        requiresAuth: true,
        adminOnly: true,
      },
    },
    {
      path: "/admin/dashboard",
      name: "admin-dashboard",
      redirect: "/admin",
    },

    // Gestión de productos
    {
      path: "/admin/productos",
      name: "admin-productos",
      component: () => import("@/views/admin/ProductsManagementView.vue"),
      meta: {
        title: "Gestión de Productos",
        requiresAuth: true,
        adminOnly: true,
      },
    },
    {
      path: "/admin/productos/nuevo",
      name: "admin-product-new",
      component: () => import("@/views/admin/ProductFormView.vue"),
      meta: {
        title: "Nuevo Producto",
        requiresAuth: true,
        adminOnly: true,
      },
    },
    {
      path: "/admin/productos/editar/:id",
      name: "admin-product-edit",
      component: () => import("@/views/admin/ProductFormView.vue"),
      meta: {
        title: "Editar Producto",
        requiresAuth: true,
        adminOnly: true,
      },
    },

    // Gestión de clientes
    {
      path: "/admin/clientes",
      name: "admin-clientes",
      component: () => import("@/views/admin/CustomersManagementView.vue"),
      meta: {
        title: "Gestión de Clientes",
        requiresAuth: true,
        adminOnly: true,
      },
    },

    // Gestión de ventas/pedidos
    {
      path: "/admin/ventas",
      name: "admin-ventas",
      component: () => import("@/views/admin/SalesManagementView.vue"),
      meta: {
        title: "Gestión de Ventas",
        requiresAuth: true,
        adminOnly: true,
      },
    },
    {
      path: "/admin/pedidos",
      name: "admin-pedidos",
      redirect: "/admin/ventas",
    },

    // Gestión de marketing
    {
      path: "/admin/marketing",
      name: "admin-marketing",
      component: () => import("@/views/admin/MarketingView.vue"),
      meta: {
        title: "Marketing y Promociones",
        requiresAuth: true,
        adminOnly: true,
      },
    },

    // Configuración
    {
      path: "/admin/configuracion",
      name: "admin-configuracion",
      component: () => import("@/views/admin/SettingsView.vue"),
      meta: {
        title: "Configuración",
        requiresAuth: true,
        adminOnly: true,
      },
    },

    // Ruta 404
    {
      path: "/:pathMatch(.*)*",
      name: "not-found",
      component: () => import("@/views/NotFoundView.vue"),
      meta: {
        title: "Página no encontrada",
        public: true,
      },
    },
  ],
});

// Navigation guard for protected routes
router.beforeEach(async (to, _from, next) => {
  // Import authStore inside the guard to avoid circular dependency
  const { useAuthStore } = await import("@/stores/auth");
  const authStore = useAuthStore();

  // Update page title
  document.title = to.meta.title || appConfig.business.name;

  // Update meta description if available
  if (to.meta.description) {
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute("content", to.meta.description);
    }
  }

  // Check if route requires authentication
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      next({
        name: "login",
        query: { redirect: to.fullPath }, // Guardar ruta para redirección después del login
      });
      return;
    }

    // Check if route requires admin privileges
    if (to.meta.adminOnly && !authStore.isAdmin) {
      next({ name: "home" });
      return;
    }
  }

  // Redirect authenticated users away from login page
  if (to.meta.hideForAuth && authStore.isAuthenticated) {
    next({ name: "admin" });
    return;
  }

  next();
});

export default router;
