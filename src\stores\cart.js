import { defineStore } from "pinia";
import { ref, computed, watch } from "vue";
import { appConfig } from "@/config/app.js";

export const useCartStore = defineStore("cart", () => {
  // State - Load from localStorage if enabled
  const items = ref(
    appConfig.store.cart.saveToLocalStorage 
      ? JSON.parse(localStorage.getItem("cart-items") || "[]")
      : []
  );
  const isModalOpen = ref(false);
  const showNotification = ref(false);
  const lastAddedProduct = ref(null);

  // Watch for changes and persist to localStorage if enabled
  if (appConfig.store.cart.saveToLocalStorage) {
    watch(
      items,
      (newItems) => {
        localStorage.setItem("cart-items", JSON.stringify(newItems));
      },
      { deep: true }
    );
  }

  // Getters
  const itemCount = computed(() => {
    return items.value.reduce((total, item) => total + item.quantity, 0);
  });

  const totalPrice = computed(() => {
    return items.value.reduce(
      (total, item) => total + item.price * item.quantity,
      0
    );
  });

  const formattedTotal = computed(() => {
    const currency = appConfig.store.currency;
    const formatter = new Intl.NumberFormat(appConfig.locale.language, {
      style: "currency",
      currency: currency.code,
      minimumFractionDigits: currency.decimals,
      maximumFractionDigits: currency.decimals,
    });
    return formatter.format(totalPrice.value);
  });

  const isEmpty = computed(() => items.value.length === 0);

  const isAtMaxCapacity = computed(() => {
    return itemCount.value >= appConfig.store.cart.maxItems;
  });

  // Actions
  function addItem(product) {
    // Check max capacity
    if (isAtMaxCapacity.value) {
      console.warn(`Cart is at maximum capacity (${appConfig.store.cart.maxItems} items)`);
      return { success: false, error: "Cart is full" };
    }

    // For configurable products, each configuration is unique
    // so we should not look for existing items, always add new
    if (product.configuration || product.customizable) {
      const cartItem = {
        id: `${product.id}-${Date.now()}`, // Unique ID for configured products
        productId: product.id, // Original product ID
        name: product.name,
        price: product.price,
        image: product.imageUrl || product.image,
        category: product.category,
        quantity: 1,
        configuration: product.configuration || null,
        specifications: product.specifications || null,
        addedAt: new Date(),
      };
      
      items.value.push(cartItem);
    } else {
      // For simple products, look for existing item and increase quantity
      const existingItem = items.value.find(
        (item) => item.productId === product.id && !item.configuration
      );

      if (existingItem) {
        existingItem.quantity += 1;
        existingItem.updatedAt = new Date();
      } else {
        const cartItem = {
          id: `${product.id}-${Date.now()}`,
          productId: product.id,
          name: product.name,
          price: product.price,
          image: product.imageUrl || product.image,
          category: product.category,
          quantity: 1,
          specifications: product.specifications || null,
          addedAt: new Date(),
        };
        
        items.value.push(cartItem);
      }
    }

    // Trigger notification
    lastAddedProduct.value = product.name;
    showNotification.value = true;

    // Auto-hide notification after 3 seconds
    setTimeout(() => {
      showNotification.value = false;
    }, 3000);

    return { success: true };
  }

  function removeItem(itemId) {
    const index = items.value.findIndex((item) => item.id === itemId);
    if (index > -1) {
      const removedItem = items.value.splice(index, 1)[0];
      return { success: true, removedItem };
    }
    return { success: false, error: "Item not found" };
  }

  function updateQuantity(itemId, quantity) {
    const item = items.value.find((item) => item.id === itemId);
    if (item) {
      if (quantity <= 0) {
        return removeItem(itemId);
      } else {
        item.quantity = Math.min(quantity, 99); // Max quantity per item
        item.updatedAt = new Date();
        return { success: true };
      }
    }
    return { success: false, error: "Item not found" };
  }

  function clearCart() {
    const itemCount = items.value.length;
    items.value = [];
    return { success: true, clearedItems: itemCount };
  }

  function openModal() {
    isModalOpen.value = true;
  }

  function closeModal() {
    isModalOpen.value = false;
  }

  function toggleModal() {
    isModalOpen.value = !isModalOpen.value;
  }

  // Utility functions
  function getItemById(itemId) {
    return items.value.find((item) => item.id === itemId);
  }

  function getItemsByProduct(productId) {
    return items.value.filter((item) => item.productId === productId);
  }

  function hasProduct(productId) {
    return items.value.some((item) => item.productId === productId);
  }

  // Session management
  function cleanExpiredItems() {
    if (!appConfig.store.cart.sessionTimeout) return;
    
    const now = new Date();
    const timeout = appConfig.store.cart.sessionTimeout;
    
    items.value = items.value.filter((item) => {
      const addedAt = new Date(item.addedAt);
      return (now - addedAt) < timeout;
    });
  }

  return {
    // State
    items,
    isModalOpen,
    showNotification,
    lastAddedProduct,
    // Getters
    itemCount,
    totalPrice,
    formattedTotal,
    isEmpty,
    isAtMaxCapacity,
    // Actions
    addItem,
    removeItem,
    updateQuantity,
    clearCart,
    openModal,
    closeModal,
    toggleModal,
    getItemById,
    getItemsByProduct,
    hasProduct,
    cleanExpiredItems,
  };
});
