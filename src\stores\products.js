import { defineStore } from "pinia";
import { ref, computed } from "vue";
import {
  collection,
  addDoc,
  getDocs,
  doc,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
} from "firebase/firestore";
import { db } from "@/main.js";
import { appConfig } from "@/config/app.js";
import { sampleProducts } from "@/data/sampleProducts.js";

export const useProductsStore = defineStore("products", () => {
  // State
  const products = ref([]);
  const loading = ref(false);
  const error = ref(null);
  const selectedCategory = ref("all");
  const searchQuery = ref("");

  // Configuration for customizable products (adaptable to different business types)
  const customizationConfig = ref({
    // Example for configurable products (can be cakes, custom items, etc.)
    sizes: [
      { id: "small", name: "Pequeño", multiplier: 0.8 },
      { id: "medium", name: "Mediano", multiplier: 1.0 },
      { id: "large", name: "<PERSON>", multiplier: 1.3 },
      { id: "xlarge", name: "Extra Grande", multiplier: 1.6 },
    ],
    customOptions: [
      { id: "option1", name: "Opción 1", price: 0, category: "basic" },
      { id: "option2", name: "Opción 2", price: 5, category: "basic" },
      { id: "option3", name: "Opción 3", price: 10, category: "premium" },
    ],
    extras: [
      {
        id: "extra1",
        name: "Extra personalizado",
        price: "variable",
        description: "Precio según especificaciones",
      },
      {
        id: "extra2",
        name: "Empaque especial",
        price: 15,
        description: "Empaque premium",
      },
    ],
  });

  // Categories from app config
  const categories = computed(() => [
    { id: "all", name: "Todos", icon: "🛍️" },
    ...appConfig.store.categories,
  ]);

  // Helper function to prioritize featured and configurable products
  const prioritizeProducts = (productList) => {
    const featured = productList.filter(p => p.featured);
    const configurable = productList.filter(
      p => p.productType === "configurable" || p.customizable === true
    );
    const regular = productList.filter(
      p => !p.featured && !(p.productType === "configurable" || p.customizable === true)
    );
    
    // Remove duplicates and maintain order: featured configurable, featured regular, configurable, regular
    const featuredConfigurable = featured.filter(
      p => p.productType === "configurable" || p.customizable === true
    );
    const featuredRegular = featured.filter(
      p => !(p.productType === "configurable" || p.customizable === true)
    );
    const nonFeaturedConfigurable = configurable.filter(p => !p.featured);
    
    return [...featuredConfigurable, ...featuredRegular, ...nonFeaturedConfigurable, ...regular];
  };

  // Getters
  const featuredProducts = computed(() => {
    const featured = products.value.filter(
      (product) => product.featured && product.active !== false
    );
    return prioritizeProducts(featured);
  });

  const filteredProducts = computed(() => {
    let filtered = products.value.filter((product) => product.active !== false);

    // Filter by category
    if (selectedCategory.value !== "all") {
      filtered = filtered.filter(
        (product) => product.category === selectedCategory.value
      );
    }

    // Filter by search query
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      filtered = filtered.filter(
        (product) =>
          product.name.toLowerCase().includes(query) ||
          product.description.toLowerCase().includes(query) ||
          (product.tags && product.tags.some(tag => 
            tag.toLowerCase().includes(query)
          ))
      );
    }

    // Prioritize featured and configurable products
    return prioritizeProducts(filtered);
  });

  // Actions
  async function fetchProducts() {
    try {
      loading.value = true;
      error.value = null;

      if (appConfig.development.mockData) {
        // Use sample data for development
        await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network delay
        products.value = sampleProducts.map(product => ({
          ...product,
          active: product.active !== false,
        }));
        return { success: true };
      }

      // Production: fetch from Firebase
      const q = query(collection(db, "products"), orderBy("createdAt", "desc"));
      const querySnapshot = await getDocs(q);

      products.value = querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));

      return { success: true };
    } catch (err) {
      error.value = err.message;
      console.error("Error fetching products:", err);
      return { success: false, error: err.message };
    } finally {
      loading.value = false;
    }
  }

  async function addProduct(productData) {
    try {
      loading.value = true;
      error.value = null;

      const newProduct = {
        ...productData,
        createdAt: new Date(),
        updatedAt: new Date(),
        active: productData.active !== undefined ? productData.active : true,
      };

      const docRef = await addDoc(collection(db, "products"), newProduct);

      // Add to local state
      products.value.unshift({
        id: docRef.id,
        ...newProduct,
      });

      return { success: true, productId: docRef.id };
    } catch (err) {
      error.value = err.message;
      console.error("Error adding product:", err);
      return { success: false, error: err.message };
    } finally {
      loading.value = false;
    }
  }

  async function updateProduct(productId, productData) {
    try {
      loading.value = true;
      error.value = null;

      const updatedProduct = {
        ...productData,
        updatedAt: new Date(),
      };

      await updateDoc(doc(db, "products", productId), updatedProduct);

      // Update local state
      const index = products.value.findIndex((p) => p.id === productId);
      if (index !== -1) {
        products.value[index] = { id: productId, ...updatedProduct };
      }

      return { success: true };
    } catch (err) {
      error.value = err.message;
      console.error("Error updating product:", err);
      return { success: false, error: err.message };
    } finally {
      loading.value = false;
    }
  }

  async function deleteProduct(productId) {
    try {
      loading.value = true;
      error.value = null;

      await deleteDoc(doc(db, "products", productId));

      // Remove from local state
      const index = products.value.findIndex((p) => p.id === productId);
      if (index !== -1) {
        products.value.splice(index, 1);
      }

      return { success: true };
    } catch (err) {
      error.value = err.message;
      console.error("Error deleting product:", err);
      return { success: false, error: err.message };
    } finally {
      loading.value = false;
    }
  }

  // Utility functions
  function setCategory(categoryId) {
    selectedCategory.value = categoryId;
  }

  function setSearchQuery(query) {
    searchQuery.value = query;
  }

  function clearFilters() {
    selectedCategory.value = "all";
    searchQuery.value = "";
  }

  function formatPrice(price) {
    const currency = appConfig.store.currency;
    const formatter = new Intl.NumberFormat(appConfig.locale.language, {
      style: "currency",
      currency: currency.code,
      minimumFractionDigits: currency.decimals,
      maximumFractionDigits: currency.decimals,
    });
    
    return formatter.format(price);
  }

  function getProductById(id) {
    return products.value.find((product) => product.id === id);
  }

  function getProductImageUrl(product, size = "medium") {
    if (product.imageUrl) {
      return product.imageUrl;
    }
    // Fallback to placeholder or category emoji
    return null;
  }

  function clearError() {
    error.value = null;
  }

  return {
    // State
    products,
    loading,
    error,
    selectedCategory,
    searchQuery,
    customizationConfig,
    // Getters
    categories,
    featuredProducts,
    filteredProducts,
    // Actions
    fetchProducts,
    addProduct,
    updateProduct,
    deleteProduct,
    setCategory,
    setSearchQuery,
    clearFilters,
    formatPrice,
    getProductById,
    getProductImageUrl,
    clearError,
  };
});
