@tailwind base;
@tailwind components;
@tailwind utilities;

/* Variables CSS para el sistema de temas */
:root {
  /* Colores por defecto - se sobrescriben con JavaScript */
  --color-primary: #3b82f6;
  --color-primary-dark: #1d4ed8;
  --color-primary-light: #93c5fd;

  --color-secondary: #6366f1;
  --color-secondary-dark: #4338ca;
  --color-secondary-light: #a5b4fc;

  --color-accent: #10b981;
  --color-accent-dark: #047857;
  --color-accent-light: #6ee7b7;

  /* Colores neutros */
  --color-neutral-50: #f9fafb;
  --color-neutral-100: #f3f4f6;
  --color-neutral-200: #e5e7eb;
  --color-neutral-300: #d1d5db;
  --color-neutral-400: #9ca3af;
  --color-neutral-500: #6b7280;
  --color-neutral-600: #4b5563;
  --color-neutral-700: #374151;
  --color-neutral-800: #1f2937;
  --color-neutral-900: #111827;

  /* Estados */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  /* Fuentes */
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-display: 'Inter', system-ui, sans-serif;
  --font-mono: 'JetBrains Mono', monospace;

  /* Espaciado */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Bordes */
  --border-radius-sm: 0.375rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;

  /* Sombras */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

@layer base {
  /* Reset y configuración base */
  * {
    box-sizing: border-box;
  }

  html {
    font-family: var(--font-sans);
    overflow-x: hidden;
    width: 100%;
    scroll-behavior: smooth;
  }

  body {
    font-family: var(--font-sans);
    color: var(--color-neutral-800);
    background-color: var(--color-neutral-50);
    padding-bottom: env(safe-area-inset-bottom);
    overflow-x: hidden;
    width: 100%;
    position: relative;
    line-height: 1.6;
  }

  @media (max-width: 48rem) {
    body {
      padding-bottom: calc(env(safe-area-inset-bottom) + 6.25rem);
    }
  }

  /* Tipografía */
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-display);
    font-weight: 600;
    line-height: 1.2;
    color: var(--color-neutral-900);
  }

  h1 { font-size: clamp(1.875rem, 4vw, 3rem); }
  h2 { font-size: clamp(1.5rem, 3.5vw, 2.25rem); }
  h3 { font-size: clamp(1.25rem, 3vw, 1.875rem); }
  h4 { font-size: clamp(1.125rem, 2.5vw, 1.5rem); }
  h5 { font-size: clamp(1rem, 2vw, 1.25rem); }
  h6 { font-size: clamp(0.875rem, 1.5vw, 1.125rem); }

  /* Enlaces */
  a {
    color: var(--color-primary);
    text-decoration: none;
    transition: color 0.2s ease;
  }

  a:hover {
    color: var(--color-primary-dark);
  }

  /* Formularios */
  input, textarea, select {
    font-family: var(--font-sans);
  }

  /* Botones */
  button {
    font-family: var(--font-sans);
    cursor: pointer;
  }

  button:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

@layer components {
  /* Botones base */
  .btn {
    @apply font-semibold rounded-lg transition-all duration-200 transform;
    font-size: clamp(0.875rem, 2.5vw, 1rem);
    min-height: 2.75rem;
    touch-action: manipulation;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
  }

  .btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
  }

  .btn:active {
    transform: translateY(0);
  }

  /* Variantes de botones */
  .btn-primary {
    background-color: var(--color-primary);
    color: white;
    box-shadow: var(--shadow-md);
  }

  .btn-primary:hover {
    background-color: var(--color-primary-dark);
  }

  .btn-secondary {
    background-color: var(--color-secondary);
    color: white;
    box-shadow: var(--shadow-md);
  }

  .btn-secondary:hover {
    background-color: var(--color-secondary-dark);
  }

  .btn-accent {
    background-color: var(--color-accent);
    color: white;
    box-shadow: var(--shadow-md);
  }

  .btn-accent:hover {
    background-color: var(--color-accent-dark);
  }

  .btn-outline {
    background-color: transparent;
    color: var(--color-primary);
    border: 2px solid var(--color-primary);
  }

  .btn-outline:hover {
    background-color: var(--color-primary);
    color: white;
  }

  .btn-ghost {
    background-color: transparent;
    color: var(--color-neutral-700);
  }

  .btn-ghost:hover {
    background-color: var(--color-neutral-100);
  }

  /* Tamaños de botones */
  .btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
    min-height: 2.25rem;
  }

  .btn-lg {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: 1.125rem;
    min-height: 3.25rem;
  }

  /* Cards */
  .card {
    background-color: white;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    overflow: hidden;
  }

  .card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
  }

  .card-body {
    padding: var(--spacing-lg);
  }

  .card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--color-neutral-200);
  }

  .card-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--color-neutral-200);
    background-color: var(--color-neutral-50);
  }

  /* Campos de entrada */
  .input-field {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--color-neutral-300);
    border-radius: var(--border-radius-lg);
    font-size: clamp(1rem, 2.5vw, 1.125rem);
    min-height: clamp(2.75rem, 6vw, 3rem);
    touch-action: manipulation;
    transition: all 0.2s ease;
    background-color: white;
    color: var(--color-neutral-800);
  }

  .input-field:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .input-field:disabled {
    background-color: var(--color-neutral-100);
    color: var(--color-neutral-500);
    cursor: not-allowed;
  }

  /* Labels */
  .label {
    display: block;
    font-weight: 500;
    color: var(--color-neutral-700);
    margin-bottom: var(--spacing-sm);
    font-size: 0.875rem;
  }

  .label-required::after {
    content: ' *';
    color: var(--color-error);
  }

  /* Gradientes */
  .gradient-bg {
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary), var(--color-accent));
  }

  .gradient-primary {
    background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  }

  .gradient-secondary {
    background: linear-gradient(135deg, var(--color-secondary), var(--color-secondary-dark));
  }

  .text-gradient {
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* Estados de alerta */
  .alert {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-lg);
    border-left: 4px solid;
    margin-bottom: var(--spacing-md);
  }

  .alert-success {
    background-color: rgba(16, 185, 129, 0.1);
    border-color: var(--color-success);
    color: #065f46;
  }

  .alert-warning {
    background-color: rgba(245, 158, 11, 0.1);
    border-color: var(--color-warning);
    color: #92400e;
  }

  .alert-error {
    background-color: rgba(239, 68, 68, 0.1);
    border-color: var(--color-error);
    color: #991b1b;
  }

  .alert-info {
    background-color: rgba(59, 130, 246, 0.1);
    border-color: var(--color-info);
    color: #1e40af;
  }

  /* Badges */
  .badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .badge-primary {
    background-color: var(--color-primary);
    color: white;
  }

  .badge-secondary {
    background-color: var(--color-secondary);
    color: white;
  }

  .badge-success {
    background-color: var(--color-success);
    color: white;
  }

  .badge-warning {
    background-color: var(--color-warning);
    color: white;
  }

  .badge-error {
    background-color: var(--color-error);
    color: white;
  }

  /* Utilidades responsive */
  .p-responsive {
    padding: clamp(1rem, 3vw, 1.5rem);
  }

  .px-responsive {
    padding-left: clamp(1rem, 3vw, 1.5rem);
    padding-right: clamp(1rem, 3vw, 1.5rem);
  }

  .py-responsive {
    padding-top: clamp(1rem, 3vw, 1.5rem);
    padding-bottom: clamp(1rem, 3vw, 1.5rem);
  }

  .space-responsive > * + * {
    margin-top: clamp(0.5rem, 2vw, 1rem);
  }

  .text-responsive-sm {
    font-size: clamp(0.875rem, 2vw, 1rem);
  }

  .text-responsive-base {
    font-size: clamp(1rem, 2.5vw, 1.125rem);
  }

  .text-responsive-lg {
    font-size: clamp(1.125rem, 3vw, 1.25rem);
  }

  /* Contenedores */
  .container-custom {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
  }

  @media (min-width: 640px) {
    .container-custom {
      padding: 0 var(--spacing-lg);
    }
  }

  @media (min-width: 1024px) {
    .container-custom {
      padding: 0 var(--spacing-xl);
    }
  }

  /* Prevenir scroll horizontal */
  .container, .max-w-7xl, .max-w-6xl, .max-w-5xl, .max-w-4xl {
    max-width: 100vw !important;
    overflow-x: hidden;
  }

  img {
    max-width: 100%;
    height: auto;
  }

  .flex, .grid {
    min-width: 0;
  }

  /* Animaciones */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideUp {
    from {
      transform: translateY(10px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slideDown {
    from {
      transform: translateY(-10px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  /* Utilidades de estado */
  .loading {
    position: relative;
    pointer-events: none;
  }

  .loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--color-neutral-300);
    border-top-color: var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    to { transform: rotate(360deg); }
  }

  /* Utilidades de texto */
  .text-balance {
    text-wrap: balance;
  }

  .text-pretty {
    text-wrap: pretty;
  }

  /* Utilidades de layout */
  .aspect-square {
    aspect-ratio: 1 / 1;
  }

  .aspect-video {
    aspect-ratio: 16 / 9;
  }

  .aspect-photo {
    aspect-ratio: 4 / 3;
  }
}
