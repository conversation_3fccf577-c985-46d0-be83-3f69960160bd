/**
 * Utilidades para subir imágenes a GitHub
 * 
 * Este archivo contiene funciones para subir imágenes al repositorio de GitHub
 * que se usa como CDN para las imágenes de productos.
 */

// Configuración del repositorio GitHub
const GITHUB_CONFIG = {
  owner: 'maikostudios',
  repo: 'assets_delicias_tia_jovy',
  branch: 'main',
  basePath: 'assets/img/productos'
}

/**
 * Convierte un archivo a base64
 */
function fileToBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => {
      // Remover el prefijo data:image/...;base64,
      const base64 = reader.result.split(',')[1]
      resolve(base64)
    }
    reader.onerror = error => reject(error)
  })
}

/**
 * Genera un nombre único para el archivo
 */
function generateUniqueFileName(originalName, category) {
  const timestamp = Date.now()
  const randomId = Math.random().toString(36).substring(2, 8)
  const extension = originalName.split('.').pop()
  const baseName = originalName.split('.')[0].toLowerCase().replace(/[^a-z0-9]/g, '-')
  
  return `${category.toLowerCase()}/${baseName}-${timestamp}-${randomId}.${extension}`
}

/**
 * Sube una imagen individual a GitHub
 */
export async function uploadImageToGitHub(file, category = 'General', githubToken = null) {
  try {
    if (!githubToken) {
      throw new Error('Token de GitHub requerido para subir imágenes')
    }

    // Convertir archivo a base64
    const base64Content = await fileToBase64(file)
    
    // Generar nombre único
    const fileName = generateUniqueFileName(file.name, category)
    const filePath = `${GITHUB_CONFIG.basePath}/${fileName}`
    
    // Preparar datos para la API de GitHub
    const uploadData = {
      message: `Add product image: ${fileName}`,
      content: base64Content,
      branch: GITHUB_CONFIG.branch
    }
    
    // URL de la API de GitHub
    const apiUrl = `https://api.github.com/repos/${GITHUB_CONFIG.owner}/${GITHUB_CONFIG.repo}/contents/${filePath}`
    
    // Realizar la petición
    const response = await fetch(apiUrl, {
      method: 'PUT',
      headers: {
        'Authorization': `token ${githubToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/vnd.github.v3+json'
      },
      body: JSON.stringify(uploadData)
    })
    
    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(`Error ${response.status}: ${errorData.message || response.statusText}`)
    }
    
    const result = await response.json()
    
    return {
      success: true,
      data: {
        name: file.name,
        path: filePath,
        url: result.content.download_url,
        rawUrl: `https://raw.githubusercontent.com/${GITHUB_CONFIG.owner}/${GITHUB_CONFIG.repo}/${GITHUB_CONFIG.branch}/${filePath}`,
        sha: result.content.sha,
        size: file.size,
        category: category
      }
    }
    
  } catch (error) {
    console.error('Error uploading image to GitHub:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * Sube múltiples imágenes en lote
 */
export async function batchUploadImages(files, category = 'General', githubToken = null, onProgress = null) {
  const results = []
  const total = files.length
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i]
    
    // Notificar progreso
    if (onProgress) {
      onProgress({
        current: i + 1,
        total: total,
        fileName: file.name,
        percentage: Math.round(((i + 1) / total) * 100)
      })
    }
    
    // Subir archivo
    const result = await uploadImageToGitHub(file, category, githubToken)
    results.push({
      file: file.name,
      ...result
    })
    
    // Pequeña pausa entre uploads para evitar rate limiting
    if (i < files.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
  }
  
  return results
}

/**
 * Valida si un archivo es una imagen válida
 */
export function validateImageFile(file) {
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
  const maxSize = 5 * 1024 * 1024 // 5MB
  
  if (!validTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Tipo de archivo no válido. Solo se permiten: JPG, PNG, GIF, WebP'
    }
  }
  
  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'El archivo es demasiado grande. Máximo 5MB'
    }
  }
  
  return { valid: true }
}

/**
 * Obtiene el token de GitHub desde localStorage o variables de entorno
 */
export function getGitHubToken() {
  // Intentar obtener desde localStorage (para desarrollo)
  const localToken = localStorage.getItem('github_token')
  if (localToken) {
    return localToken
  }
  
  // Intentar obtener desde variables de entorno
  const envToken = import.meta.env.VITE_GITHUB_TOKEN
  if (envToken) {
    return envToken
  }
  
  return null
}

/**
 * Configura el token de GitHub en localStorage
 */
export function setGitHubToken(token) {
  if (token) {
    localStorage.setItem('github_token', token)
  } else {
    localStorage.removeItem('github_token')
  }
}
