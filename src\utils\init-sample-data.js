/**
 * Utilidades para inicializar datos de muestra en Firebase
 */

import { collection, addDoc, getDocs, deleteDoc } from "firebase/firestore";
import { db } from "@/main.js";
import { sampleProducts } from "@/data/sampleProducts.js";
import { sampleOrders } from "@/data/sampleOrders.js";
import { sampleCustomers } from "@/data/sampleCustomers.js";

/**
 * Verifica si ya existen productos en Firestore
 */
export async function checkProductsExist() {
  try {
    const productsRef = collection(db, "products");
    const snapshot = await getDocs(productsRef);
    return !snapshot.empty;
  } catch (error) {
    console.error("Error checking products:", error);
    return false;
  }
}

/**
 * Inicializa productos de muestra en Firestore
 */
async function initializeProducts() {
  try {
    const productsRef = collection(db, "products");

    for (const product of sampleProducts) {
      await addDoc(productsRef, {
        ...product,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    return { success: true, count: sampleProducts.length };
  } catch (error) {
    console.error("Error initializing products:", error);
    return { success: false, error: error.message };
  }
}

/**
 * Inicializa clientes de muestra en Firestore
 */
async function initializeCustomers() {
  try {
    const customersRef = collection(db, "customers");

    for (const customer of sampleCustomers) {
      await addDoc(customersRef, {
        ...customer,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    return { success: true, count: sampleCustomers.length };
  } catch (error) {
    console.error("Error initializing customers:", error);
    return { success: false, error: error.message };
  }
}

/**
 * Inicializa pedidos de muestra en Firestore
 */
async function initializeOrders() {
  try {
    const ordersRef = collection(db, "orders");

    for (const order of sampleOrders) {
      await addDoc(ordersRef, {
        ...order,
        createdAt: new Date(order.createdAt || new Date()),
        updatedAt: new Date(),
      });
    }

    return { success: true, count: sampleOrders.length };
  } catch (error) {
    console.error("Error initializing orders:", error);
    return { success: false, error: error.message };
  }
}

/**
 * Función principal para inicializar todos los datos de muestra
 */
export async function initializeSampleData() {
  try {
    // Verificar si ya existen productos
    const productsExist = await checkProductsExist();

    if (productsExist) {
      return {
        success: false,
        error:
          "Los productos ya existen en la base de datos. Elimina los datos existentes primero.",
      };
    }

    // Inicializar productos
    const productsResult = await initializeProducts();
    if (!productsResult.success) {
      return {
        success: false,
        error: `Error inicializando productos: ${productsResult.error}`,
      };
    }

    // Inicializar clientes
    const customersResult = await initializeCustomers();
    if (!customersResult.success) {
      console.warn("Error inicializando clientes:", customersResult.error);
    }

    // Inicializar pedidos
    const ordersResult = await initializeOrders();
    if (!ordersResult.success) {
      console.warn("Error inicializando pedidos:", ordersResult.error);
    }

    return {
      success: true,
      message: `Datos inicializados exitosamente: ${productsResult.count} productos, ${
        customersResult.count || 0
      } clientes, ${ordersResult.count || 0} pedidos`,
    };
  } catch (error) {
    console.error("Error general inicializando datos:", error);
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Función para limpiar todos los datos de muestra
 */
export async function clearSampleData() {
  try {
    const collections = ["products", "customers", "orders"];

    for (const collectionName of collections) {
      const collectionRef = collection(db, collectionName);
      const snapshot = await getDocs(collectionRef);

      const deletePromises = snapshot.docs.map((doc) => deleteDoc(doc.ref));
      await Promise.all(deletePromises);
    }

    return {
      success: true,
      message: "Todos los datos de muestra han sido eliminados",
    };
  } catch (error) {
    console.error("Error limpiando datos:", error);
    return {
      success: false,
      error: error.message,
    };
  }
}
