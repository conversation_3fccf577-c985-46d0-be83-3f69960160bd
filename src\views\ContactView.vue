<template>
    <div class="min-h-screen bg-gray-50">
        <!-- Header -->
        <div class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <h1 class="text-2xl font-bold text-gray-900">Contáctanos</h1>
                <p class="text-gray-600">Estamos aquí para ayudarte. Completa el formulario o utiliza los enlaces
                    rápidos para comunicarte con nosotros.</p>
            </div>
        </div>

        <!-- Main Content -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Contact Form -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-6">Formulario de Contacto</h2>
                    <form @submit.prevent="handleSubmit">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Nombre *</label>
                            <input v-model="form.name" type="text" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                placeholder="Tu nombre" />
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Correo Electrónico *</label>
                            <input v-model="form.email" type="email" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                placeholder="Tu correo electrónico" />
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Mensaje *</label>
                            <textarea v-model="form.message" required rows="4"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                placeholder="Escribe tu mensaje"></textarea>
                        </div>
                        <button type="submit"
                            class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors">Enviar</button>
                    </form>
                </div>

                <!-- Quick Links -->
                <div class="space-y-6">
                    <div class="bg-white rounded-lg shadow p-6">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">Enlaces Rápidos</h2>
                        <ul class="space-y-2">
                            <li>
                                <a href="https://wa.me/56984630545" target="_blank"
                                    class="text-primary hover:underline">📱 WhatsApp</a>
                            </li>
                            <li>
                                <a href="mailto:<EMAIL>" class="text-primary hover:underline">✉️
                                    Correo Electrónico</a>
                            </li>
                        </ul>
                    </div>
                    <Footer />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import Footer from '@/components/layout/Footer.vue';
import { useNotificationsStore } from '@/stores/notifications';

const notificationsStore = useNotificationsStore();

const form = ref({
    name: '',
    email: '',
    message: ''
});

const handleSubmit = async () => {
    try {
        // Simular envío de mensaje
        console.log('Enviando mensaje:', form.value);
        notificationsStore.success('Mensaje enviado', 'Gracias por contactarnos. Te responderemos pronto.');
        form.value = { name: '', email: '', message: '' };
    } catch (error) {
        notificationsStore.error('Error al enviar', 'No se pudo enviar tu mensaje. Intenta nuevamente.');
    }
};

// Add JSON-LD for contact information
const contactSchema = {
  '@context': 'https://schema.org',
  '@type': 'ContactPoint',
  telephone: '+56984630545',
  email: '<EMAIL>',
  contactType: 'Customer Service'
};
</script>

<style scoped>
/* Estilos adicionales si son necesarios */
</style>
